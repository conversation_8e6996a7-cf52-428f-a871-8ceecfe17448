<!DOCTYPE html>
<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>IsotopeAI: Focus, Track, Achieve</title>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&amp;display=swap" rel="stylesheet"/>

<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<script>tailwind.config = {
        darkMode: "class",
        theme: {
            extend: {
                colors: {
                    primary: "#F4C753",
                    "background-light": "#f8f7f6",
                    "background-dark": "#221d10"
                },
                fontFamily: {
                    display: "Inter"
                },
                borderRadius: {
                    DEFAULT: "0.5rem",
                    lg: "1rem",
                    xl: "1.5rem",
                    full: "9999px"
                }
            }
        }
    }</script>
<style>body {
        min-height: max(884px, 100dvh);
    }</style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-background-light dark:bg-background-dark font-display text-gray-800 dark:text-gray-200">
<div class="relative flex min-h-screen w-full flex-col">
<header class="sticky top-0 z-10 bg-gradient-to-b from-background-light/95 to-background-light/80 dark:from-background-dark/95 dark:to-background-dark/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50">
<div class="flex items-center justify-between px-6 py-4">
<div class="w-10"></div>
<div class="flex flex-col items-center">
<h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">Focus</h1>
<p class="text-xs text-gray-500 dark:text-gray-400 font-medium">Session</p>
</div>
<button class="flex h-10 w-10 items-center justify-center rounded-xl bg-gray-100/80 dark:bg-gray-800/80 text-gray-600 dark:text-gray-400 hover:bg-gray-200/80 dark:hover:bg-gray-700/80 transition-colors">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="20" height="20">
<path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"/>
<path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
</svg>
</button>
</div>
</header>
<main class="flex-grow p-4 pt-12 space-y-8">
<div class="flex flex-col items-center justify-center space-y-6">
<div class="relative w-80 h-80">
          <div class="absolute inset-0 bg-gradient-to-tr from-primary/50 to-blue-500/50 rounded-full blur-3xl opacity-50 animate-pulse"></div>
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="w-72 h-72 bg-background-light/80 dark:bg-background-dark/80 rounded-full backdrop-blur-lg border border-white/10"></div>
          </div>
          <div class="absolute inset-0 flex flex-col items-center justify-center text-center">
            <p class="text-7xl font-bold tracking-tighter text-gray-900 dark:text-white" id="timer-display">25:00</p>
            <p class="text-base text-gray-500 dark:text-gray-400" id="timer-label">Pomodoro</p>
          </div>
        </div>
        <div id="timer-controls">
          <button class="w-60 h-16 flex items-center justify-center rounded-full bg-primary text-white text-lg font-semibold shadow-lg transform hover:scale-105 transition-transform" id="start-button">
            Start Session
          </button>
          <div class="flex items-center space-x-4 hidden" id="active-timer-buttons">
            <button class="w-28 h-14 flex items-center justify-center rounded-full bg-gray-200/50 dark:bg-gray-800/50 text-gray-500 dark:text-gray-400 hover:bg-gray-300/50 dark:hover:bg-gray-700/50 transition-colors" id="pause-button">
              Pause
            </button>
            <button class="w-28 h-14 flex items-center justify-center rounded-full bg-gray-200/50 dark:bg-gray-800/50 text-gray-500 dark:text-gray-400 hover:bg-gray-300/50 dark:hover:bg-gray-700/50 transition-colors" id="restart-button">
              Restart
            </button>
            <button class="w-28 h-14 flex items-center justify-center rounded-full bg-red-500/80 text-white hover:bg-red-600/80 transition-colors" id="end-session-button">
              End Session
            </button>
          </div>
        </div>
</div>
<div class="space-y-4">
<h3 class="text-lg font-bold text-gray-900 dark:text-white">Distraction Blocking</h3>
<div class="bg-gray-100 dark:bg-gray-800/50 rounded-xl p-4 space-y-4">
<div class="flex items-center justify-between">
<div class="flex items-center space-x-3">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-gray-500 dark:text-gray-400">
<path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"/>
</svg>
<p class="font-medium">Block Websites</p>
</div>
<label class="relative inline-flex items-center cursor-pointer">
<input checked="" class="sr-only peer" type="checkbox" value=""/>
<div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-primary rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
</label>
</div>
<div class="flex items-center justify-between">
<div class="flex items-center space-x-3">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-gray-500 dark:text-gray-400">
<path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"/>
</svg>
<p class="font-medium">Block Apps</p>
</div>
<label class="relative inline-flex items-center cursor-pointer">
<input class="sr-only peer" type="checkbox" value=""/>
<div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-primary rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
</label>
</div>
</div>
</div>
<div class="space-y-4">
<h3 class="text-lg font-bold text-gray-900 dark:text-white">Today's Subject Analytics</h3>
<div class="bg-gray-100 dark:bg-gray-800/50 rounded-xl p-4 space-y-4">
<div class="space-y-3">
<div class="flex items-center justify-between">
<span class="font-medium text-gray-800 dark:text-gray-200">Math</span>
<span class="font-medium text-gray-500 dark:text-gray-400">2h 30m</span>
</div>
<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
<div class="bg-gradient-to-r from-cyan-400 to-blue-500 h-2.5 rounded-full" style="width: 75%"></div>
</div>
</div>
<div class="space-y-3">
<div class="flex items-center justify-between">
<span class="font-medium text-gray-800 dark:text-gray-200">Physics</span>
<span class="font-medium text-gray-500 dark:text-gray-400">1h 45m</span>
</div>
<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
<div class="bg-gradient-to-r from-green-400 to-teal-500 h-2.5 rounded-full" style="width: 50%"></div>
</div>
</div>
<div class="space-y-3">
<div class="flex items-center justify-between">
<span class="font-medium text-gray-800 dark:text-gray-200">Chemistry</span>
<span class="font-medium text-gray-500 dark:text-gray-400">1h 15m</span>
</div>
<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
<div class="bg-gradient-to-r from-yellow-400 to-orange-500 h-2.5 rounded-full" style="width: 35%"></div>
</div>
</div>
<div class="space-y-3">
<div class="flex items-center justify-between">
<span class="font-medium text-gray-800 dark:text-gray-200">Biology</span>
<span class="font-medium text-gray-500 dark:text-gray-400">0h 45m</span>
</div>
<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
<div class="bg-gradient-to-r from-pink-400 to-red-500 h-2.5 rounded-full" style="width: 20%"></div>
</div>
</div>
</div>
</div>
</main>
<footer class="fixed bottom-0 w-full bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-800/50 shadow-lg">
<div class="flex justify-around items-center px-4 py-3 safe-area-inset-bottom">
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-700 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="dashboard.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/>
</svg>
</div>
<p class="text-xs font-medium">Home</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl bg-primary/10 text-primary" href="focus.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
</svg>
</div>
<p class="text-xs font-semibold">Focus</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-700 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="track.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"/>
</svg>
</div>
<p class="text-xs font-medium">Track</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-700 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="achieve.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"/>
</svg>
</div>
<p class="text-xs font-medium">Achieve</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-700 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="tasks.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"/>
</svg>
</div>
<p class="text-xs font-medium">Tasks</p>
</a>
</div>
</footer>
</div>
<script>
  const timerDisplay = document.getElementById('timer-display');
  const startButton = document.getElementById('start-button');
  const activeTimerButtons = document.getElementById('active-timer-buttons');
  const pauseButton = document.getElementById('pause-button');
  const restartButton = document.getElementById('restart-button');
  const endSessionButton = document.getElementById('end-session-button');

  let timer;
  let timeInSeconds = 25 * 60;
  let isRunning = false;

  function updateDisplay() {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = timeInSeconds % 60;
    timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  function startTimer() {
    isRunning = true;
    pauseButton.textContent = 'Pause';
    startButton.classList.add('hidden');
    activeTimerButtons.classList.remove('hidden');
    timer = setInterval(() => {
      timeInSeconds--;
      updateDisplay();
      if (timeInSeconds <= 0) {
        clearInterval(timer);
        isRunning = false;
      }
    }, 1000);
  }

  function pauseTimer() {
    isRunning = false;
    pauseButton.textContent = 'Resume';
    clearInterval(timer);
  }

  function resumeTimer() {
    isRunning = true;
    pauseButton.textContent = 'Pause';
    timer = setInterval(() => {
      timeInSeconds--;
      updateDisplay();
      if (timeInSeconds <= 0) {
        clearInterval(timer);
        isRunning = false;
      }
    }, 1000);
  }

  function restartTimer() {
    pauseTimer();
    timeInSeconds = 25 * 60;
    updateDisplay();
    resumeTimer();
  }

  function endSession() {
    pauseTimer();
    timeInSeconds = 25 * 60;
    updateDisplay();
    startButton.classList.remove('hidden');
    activeTimerButtons.classList.add('hidden');
  }

  startButton.addEventListener('click', startTimer);

  pauseButton.addEventListener('click', () => {
    if (isRunning) {
      pauseTimer();
    } else {
      resumeTimer();
    }
  });

  restartButton.addEventListener('click', restartTimer);
  endSessionButton.addEventListener('click', endSession);

  updateDisplay();
</script>

</body></html>