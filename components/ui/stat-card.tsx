import { cn } from '@/lib/utils';
import React from 'react';
import { Dimensions, Text } from 'react-native';
import Animated, { FadeInUp } from 'react-native-reanimated';

const { width: screenWidth } = Dimensions.get('window');

interface StatCardProps {
  value: string;
  label: string;
  className?: string;
  delay?: number;
  variant?: 'default' | 'compact';
  accessibilityLabel?: string;
}

export function StatCard({
  value,
  label,
  className,
  delay = 0,
  variant = 'default',
  accessibilityLabel
}: StatCardProps) {
  const isCompact = variant === 'compact' || screenWidth < 768;

  return (
    <Animated.View entering={FadeInUp.delay(delay).duration(400).springify()}>
      <Animated.View
        className={cn(
          "text-center bg-surface-light dark:bg-surface-dark rounded-xl border border-separator-light/20 dark:border-separator-dark/20",
          isCompact ? "p-3" : "p-4",
          className
        )}
        style={{
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.05,
          shadowRadius: 4,
          elevation: 2,
        }}
        accessible={true}
        accessibilityLabel={accessibilityLabel || `${label}: ${value}`}
        accessibilityRole="text"
      >
      <Text
        className={cn(
          "font-bold text-label-primary-light dark:text-label-primary-dark",
          isCompact ? "text-2xl" : "text-3xl"
        )}
        style={{ lineHeight: isCompact ? 28 : 36 }}
      >
        {value}
      </Text>
      <Text
        className={cn(
          "text-label-secondary-light/68 dark:text-label-secondary-dark/68 font-medium mt-1",
          isCompact ? "text-xs" : "text-sm"
        )}
        numberOfLines={1}
        adjustsFontSizeToFit
      >
        {label}
      </Text>
      </Animated.View>
    </Animated.View>
  );
}
