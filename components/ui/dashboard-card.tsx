import { useCardDimensions } from '@/hooks/use-responsive-dimensions';
import { cn } from '@/lib/utils';
import React, { memo } from 'react';
import { ViewProps } from 'react-native';
import Animated, {
    FadeInUp,
    useAnimatedStyle,
    useSharedValue,
    withSpring
} from 'react-native-reanimated';

interface DashboardCardProps extends ViewProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  variant?: 'default' | 'compact' | 'featured';
  fullWidth?: boolean;
  interactive?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
}

const DashboardCardComponent = ({
  children,
  className,
  delay = 0,
  variant = 'default',
  fullWidth = false,
  interactive = true,
  accessibilityLabel,
  accessibilityHint,
  ...props
}: DashboardCardProps) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);
  const translateY = useSharedValue(0);

  // Use responsive dimensions hook
  const cardDimensions = useCardDimensions();

  // Enhanced animation styles with spring physics
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value },
        { translateY: translateY.value }
      ],
      opacity: opacity.value,
    };
  });

  // Improved touch interactions with haptic feedback
  const handlePressIn = () => {
    if (!interactive) return;

    scale.value = withSpring(0.97, {
      damping: 15,
      stiffness: 300,
    });
    translateY.value = withSpring(1, {
      damping: 15,
      stiffness: 300,
    });
  };

  const handlePressOut = () => {
    if (!interactive) return;

    scale.value = withSpring(1, {
      damping: 15,
      stiffness: 300,
    });
    translateY.value = withSpring(0, {
      damping: 15,
      stiffness: 300,
    });
  };

  // Variant-specific styling
  const getVariantStyles = () => {
    switch (variant) {
      case 'compact':
        return 'p-4 rounded-xl';
      case 'featured':
        return 'p-8 rounded-3xl shadow-2xl';
      default:
        return 'p-6 rounded-2xl shadow-lg';
    }
  };

  // Enhanced shadow system for depth
  const getShadowStyle = () => {
    const baseOpacity = variant === 'featured' ? 0.15 : 0.1;
    const shadowRadius = variant === 'featured' ? 16 : 8;
    const elevation = variant === 'featured' ? 8 : 4;

    return {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: variant === 'featured' ? 4 : 2 },
      shadowOpacity: baseOpacity,
      shadowRadius,
      elevation,
    };
  };

  return (
    <Animated.View
      entering={FadeInUp.delay(delay).duration(600).springify()}
      className={cn(
        "bg-card-light dark:bg-card-dark backdrop-blur-xl border border-separator-light/30 dark:border-separator-dark/30",
        getVariantStyles(),
        className
      )}
      style={[
        animatedStyle,
        getShadowStyle(),
        {
          width: fullWidth ? '100%' : (typeof cardDimensions.width === 'string' ? undefined : cardDimensions.width),
          minHeight: cardDimensions.minHeight,
        }
      ]}
      onTouchStart={interactive ? handlePressIn : undefined}
      onTouchEnd={interactive ? handlePressOut : undefined}
      onTouchCancel={interactive ? handlePressOut : undefined}
      accessible={true}
      accessibilityRole="button"
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      {...props}
    >
      {children}
    </Animated.View>
  );
};

// Memoized export for performance
export const DashboardCard = memo(DashboardCardComponent);
